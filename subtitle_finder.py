#!/usr/bin/env python3
"""
Subtitle Finder & Downloader (Subscene Scraper with GUI)
Features:
- Search movies or TV episodes by title/year
- TV helpers: season/episode appended to query
- Language filter (e.g., english, spanish; comma-separated)
- Interactive selection + batch download via GUI
- Decompresses .zip to .srt
Prereqs: pip install subsceneAPI requests
Run: python subtitle_fetcher.py
"""
import sys
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging

# Local imports
import config
from utils import (
    sanitize_filename, validate_year,
    validate_season, validate_episode, validate_limit
)
from downloader import fetch_and_save
from subtitle_sources import MultiSourceSearcher



def create_gui():
    """Create and configure the main GUI window."""
    root = tk.Tk()
    root.title("Subtitle Finder & Downloader")
    root.geometry(config.DEFAULT_WINDOW_SIZE)

    # Status bar
    status_var = tk.StringVar()
    status_label = ttk.Label(root, textvariable=status_var, relief=tk.SUNKEN, anchor=tk.W)
    status_label.pack(side=tk.BOTTOM, fill=tk.X)

    # Input frame
    input_frame = ttk.Frame(root, padding="10")
    input_frame.pack(fill=tk.X)

    ttk.Label(input_frame, text="Query:").grid(row=0, column=0, sticky=tk.W)
    query_entry = ttk.Entry(input_frame, width=50)
    query_entry.grid(row=0, column=1, padx=5)

    ttk.Label(input_frame, text="Type:").grid(row=1, column=0, sticky=tk.W)
    type_combo = ttk.Combobox(input_frame, values=["movie", "episode"], state="readonly")
    type_combo.grid(row=1, column=1, padx=5)

    ttk.Label(input_frame, text="Languages (comma-separated):").grid(row=2, column=0, sticky=tk.W)
    languages_entry = ttk.Entry(input_frame, width=50)
    languages_entry.insert(0, config.DEFAULT_LANGUAGE)
    languages_entry.grid(row=2, column=1, padx=5)

    ttk.Label(input_frame, text="Year:").grid(row=3, column=0, sticky=tk.W)
    year_entry = ttk.Entry(input_frame, width=10)
    year_entry.grid(row=3, column=1, padx=5, sticky=tk.W)

    ttk.Label(input_frame, text="Season:").grid(row=4, column=0, sticky=tk.W)
    season_entry = ttk.Entry(input_frame, width=10)
    season_entry.grid(row=4, column=1, padx=5, sticky=tk.W)

    ttk.Label(input_frame, text="Episode:").grid(row=5, column=0, sticky=tk.W)
    episode_entry = ttk.Entry(input_frame, width=10)
    episode_entry.grid(row=5, column=1, padx=5, sticky=tk.W)

    ttk.Label(input_frame, text="Limit:").grid(row=6, column=0, sticky=tk.W)
    limit_entry = ttk.Entry(input_frame, width=10)
    limit_entry.insert(0, str(config.DEFAULT_SEARCH_LIMIT))
    limit_entry.grid(row=6, column=1, padx=5, sticky=tk.W)

    # Output directory
    ttk.Label(input_frame, text="Output Folder:").grid(row=7, column=0, sticky=tk.W)
    out_entry = ttk.Entry(input_frame, width=50)
    out_entry.insert(0, str(Path(config.DEFAULT_OUTPUT_DIR).absolute()))
    out_entry.grid(row=7, column=1, padx=5)
    def browse_out():
        dir = filedialog.askdirectory()
        if dir:
            out_entry.delete(0, tk.END)
            out_entry.insert(0, dir)
    ttk.Button(input_frame, text="Browse", command=browse_out).grid(row=7, column=2, padx=5)

    # Results frame
    results_frame = ttk.Frame(root, padding="10")
    results_frame.pack(fill=tk.BOTH, expand=True)

    tree = ttk.Treeview(results_frame, columns=("Source", "Lang", "Name/Release"), selectmode="extended")
    tree.heading("#0", text="#")
    tree.heading("Source", text="Source")
    tree.heading("Lang", text="Lang")
    tree.heading("Name/Release", text="Name/Release")
    tree.column("Source", width=100)
    tree.column("Lang", width=80)
    tree.pack(fill=tk.BOTH, expand=True)

    all_rows = []  # To store results
    searcher = MultiSourceSearcher()  # Multi-source searcher

    def validate_inputs():
        """Validate and parse user inputs."""
        query = query_entry.get().strip()
        if not query:
            raise ValueError("Query is required.")
        if len(query) > config.MAX_QUERY_LENGTH:
            raise ValueError(f"Query too long (max {config.MAX_QUERY_LENGTH} characters).")

        year_str = year_entry.get().strip()
        season_str = season_entry.get().strip()
        episode_str = episode_entry.get().strip()
        limit_str = limit_entry.get().strip()
        out_dir = out_entry.get().strip()

        # Validate numeric inputs using utility functions
        year = validate_year(year_str) if year_str else None
        season = validate_season(season_str) if season_str else None
        episode = validate_episode(episode_str) if episode_str else None
        limit = validate_limit(limit_str) if limit_str else config.DEFAULT_SEARCH_LIMIT

        # Validate output directory
        if not out_dir:
            raise ValueError("Output directory is required.")

        out_path = Path(out_dir)
        if not out_path.is_absolute():
            raise ValueError("Output directory must be an absolute path.")

        try:
            out_path.mkdir(parents=True, exist_ok=True)
            # Test write permissions
            test_file = out_path / ".test_write"
            test_file.write_text("test")
            test_file.unlink()
        except Exception as e:
            raise ValueError(f"Cannot write to output directory: {str(e)}")

        # Validate languages
        languages = languages_entry.get().strip()
        if not languages:
            raise ValueError("At least one language is required.")

        return query, type_combo.get(), languages, year, season, episode, limit, out_path

    def search_thread():
        """Search for subtitles using multiple sources."""
        try:
            status_var.set("Searching multiple sources...")
            root.update_idletasks()
            query, content_type, languages, year, season, episode, limit, _ = validate_inputs()

            # Store original query for sources that don't need episode info
            original_query = query

            # Some sources might benefit from episode-specific queries
            if content_type == "episode" and season and episode:
                episode_query = f"{query} Season {season} Episode {episode}"
            else:
                episode_query = query

            nonlocal all_rows
            all_rows.clear()
            tree.delete(*tree.get_children())

            # Search all languages across all sources
            for lang in [l.strip().lower() for l in languages.split(",") if l.strip()]:
                status_var.set(f"Searching for {lang} subtitles...")
                root.update_idletasks()

                # Try original query first (for manual database)
                results = searcher.search_combined(
                    query=original_query,
                    year=year,
                    language=lang,
                    limit=min(limit, 50)
                )

                # If no results and we have episode info, try episode-specific query
                if not results and episode_query != original_query:
                    status_var.set(f"Trying episode-specific search for {lang}...")
                    root.update_idletasks()

                    results = searcher.search_combined(
                        query=episode_query,
                        year=year,
                        language=lang,
                        limit=min(limit, 50)
                    )

                all_rows.extend(results)

            if not all_rows:
                messagebox.showinfo("Info", "No subtitles found from any source.")
                status_var.set("")
                return

            # Display results with source information
            for i, r in enumerate(all_rows, 1):
                source = r.get("source", "Unknown")
                language = r.get("language", "Unknown")
                name = r.get("name", "Unknown")
                tree.insert("", "end", text=str(i), values=(source, language, name))

            status_var.set(f"Search complete. Found {len(all_rows)} results from multiple sources.")

        except ValueError as ve:
            messagebox.showerror("Input Error", str(ve))
            status_var.set("")
        except Exception as e:
            logging.error(f"Search error: {e}")
            messagebox.showerror("Search Error", f"Failed to perform search: {str(e)}")
            status_var.set("")

    def perform_search():
        threading.Thread(target=search_thread, daemon=True).start()

    def download_thread():
        """Download selected subtitles in a separate thread."""
        success_count = 0
        error_count = 0

        try:
            root.after(0, lambda: status_var.set("Downloading..."))
            selected = tree.selection()
            if not selected:
                raise ValueError("No items selected.")

            _, _, _, _, _, _, _, out_dir = validate_inputs()

            # Validate selection indices
            picks = []
            for item in selected:
                try:
                    index = int(tree.item(item)["text"]) - 1
                    if 0 <= index < len(all_rows):
                        picks.append(all_rows[index])
                    else:
                        error_count += 1
                        logging.warning(f"Invalid selection index: {index}")
                except (ValueError, KeyError) as e:
                    error_count += 1
                    logging.warning(f"Error parsing selection: {e}")

            if not picks:
                raise ValueError("No valid items selected.")

            total = len(picks)
            for i, r in enumerate(picks, 1):
                try:
                    link = r["link"]
                    name = r["name"]

                    # Update status
                    root.after(0, lambda i=i, total=total: status_var.set(f"Downloading {i}/{total}..."))

                    # Rate limiting
                    time.sleep(config.DOWNLOAD_DELAY)

                    # Download file
                    saved = fetch_and_save(link, out_dir, preferred_name=f"{sanitize_filename(name)}.zip")
                    success_count += 1
                    logging.info(f"Downloaded: {saved}")

                except Exception as e:
                    error_count += 1
                    logging.error(f"Failed to download {name}: {e}")

            # Show final results
            if success_count > 0:
                root.after(0, lambda: messagebox.showinfo("Download Complete",
                    f"Successfully downloaded {success_count} subtitle(s).\n"
                    f"Errors: {error_count}\n"
                    f"Files saved to: {out_dir}"))

            if error_count > 0 and success_count == 0:
                root.after(0, lambda: messagebox.showerror("Download Failed",
                    f"All downloads failed. Check the log for details."))

            root.after(0, lambda: status_var.set("Download complete."))

        except ValueError as ve:
            root.after(0, lambda: messagebox.showerror("Input Error", str(ve)))
            root.after(0, lambda: status_var.set(""))
        except Exception as e:
            logging.error(f"Download thread error: {e}")
            root.after(0, lambda: messagebox.showerror("Download Error", f"Failed to download: {str(e)}"))
            root.after(0, lambda: status_var.set(""))

    def perform_download():
        threading.Thread(target=download_thread, daemon=True).start()

    # Buttons
    buttons_frame = ttk.Frame(root, padding="10")
    buttons_frame.pack(fill=tk.X)

    search_btn = ttk.Button(buttons_frame, text="Search", command=perform_search)
    search_btn.pack(side=tk.LEFT, padx=5)
    download_btn = ttk.Button(buttons_frame, text="Download Selected", command=perform_download)
    download_btn.pack(side=tk.LEFT, padx=5)
    ttk.Button(buttons_frame, text="Quit", command=root.quit).pack(side=tk.LEFT, padx=5)

    # Disable buttons during operations? For simplicity, not implementing lock, but threads prevent freeze.

    root.mainloop()

def main():
    """Main entry point for the application."""
    try:
        import subsceneAPI
        import requests
    except ImportError as e:
        print(f"Missing dependency: {e}. Please install via 'pip install subsceneAPI requests'.", file=sys.stderr)
        sys.exit(1)

    # Set up basic logging
    logging.basicConfig(level=getattr(logging, config.LOG_LEVEL), format=config.LOG_FORMAT)

    create_gui()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
"""
Quick start
1) Install deps:
   pip install subsceneAPI requests
2) Run the GUI:
   python subtitle_fetcher.py
Notes
- Enter search parameters in the GUI fields.
- Click 'Search' to fetch results.
- Select rows in the table (hold Ctrl for multiple).
- Click 'Download Selected' to save subtitles.
- Files are saved in the specified output folder (default ./subtitles).
- The tool auto-unzips .zip and writes .srt.
- Improved robustness: input validation, error handling, threading for non-blocking operations, status updates.
"""