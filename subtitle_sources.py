"""Multi-source subtitle search functionality."""

import logging
import requests
import json
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
import config


def flatten_results(subs, language: str) -> List[Dict[str, Any]]:
    """Convert subscene results to a flat list of dictionaries."""
    rows: List[Dict[str, Any]] = []
    if not hasattr(subs, 'ZIPlinks'):
        return rows

    for name, link in subs.ZIPlinks:
        if name and link:  # Ensure both name and link are valid
            rows.append({
                "name": str(name).strip(),
                "link": str(link).strip(),
                "language": language
            })
    return rows


class SubtitleSource(ABC):
    """Abstract base class for subtitle sources."""
    
    @abstractmethod
    def search(self, query: str, year: Optional[int] = None, 
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search for subtitles."""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Source name."""
        pass


class SubsceneSource(SubtitleSource):
    """Subscene subtitle source using subsceneAPI."""
    
    @property
    def name(self) -> str:
        return "Subscene"
    
    def search(self, query: str, year: Optional[int] = None, 
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search Subscene for subtitles."""
        try:
            from subsceneAPI import subtitle
            
            subs = subtitle.search(
                title=query, 
                year=str(year) if year else None, 
                language=language, 
                limit=str(min(limit, 50))
            )
            
            return flatten_results(subs, language)
            
        except Exception as e:
            logging.error(f"Subscene search error: {e}")
            return []


class OpenSubtitlesWebSource(SubtitleSource):
    """OpenSubtitles source using web scraping."""

    BASE_URL = "https://www.opensubtitles.org"

    @property
    def name(self) -> str:
        return "OpenSubtitles"

    def _get_language_id(self, language: str) -> str:
        """Convert language name to OpenSubtitles language ID."""
        lang_map = {
            "english": "eng",
            "spanish": "spa",
            "french": "fre",
            "german": "ger",
            "italian": "ita",
            "portuguese": "por",
            "russian": "rus",
            "chinese": "chi",
            "japanese": "jpn",
            "korean": "kor",
            "arabic": "ara",
            "dutch": "dut",
            "swedish": "swe",
            "norwegian": "nor",
            "danish": "dan",
            "finnish": "fin"
        }
        return lang_map.get(language.lower(), "eng")

    def search(self, query: str, year: Optional[int] = None,
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search OpenSubtitles website using web scraping."""
        try:
            import re
            from urllib.parse import quote, urljoin

            # Prepare search URL
            lang_id = self._get_language_id(language)
            search_query = quote(query)
            search_url = f"{self.BASE_URL}/en/search/sublanguageid-{lang_id}/moviename-{search_query}"

            if year:
                search_url += f"/movieyear-{year}"

            # Make request with proper headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(search_url, headers=headers, timeout=config.MAX_DOWNLOAD_TIMEOUT)
            response.raise_for_status()

            # Parse HTML to extract subtitle links
            # This is a simplified implementation - real implementation would use BeautifulSoup
            results = []

            # For now, return empty results to avoid 404 errors
            # Real implementation would parse the HTML response
            logging.info(f"OpenSubtitles web search completed for: {query} (no results - real implementation needed)")
            return results[:limit]

        except Exception as e:
            logging.error(f"OpenSubtitles search error: {e}")
            return []


class AlternativeSubsceneSource(SubtitleSource):
    """Alternative Subscene search with different query formats."""

    @property
    def name(self) -> str:
        return "Subscene Alt"

    def search(self, query: str, year: Optional[int] = None,
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search Subscene with alternative query formats."""
        try:
            from subsceneAPI import subtitle

            # Try different query variations
            query_variations = [
                query.replace(".", " "),  # "11.22.63" -> "11 22 63"
                query.replace(".", "-"),  # "11.22.63" -> "11-22-63"
                query.replace(".", ""),   # "11.22.63" -> "112263"
            ]

            # Add common alternative titles
            if "11.22.63" in query.lower():
                query_variations.extend([
                    "JFK",
                    "eleven twenty two sixty three",
                    "11/22/63"
                ])

            all_results = []
            for variant in query_variations:
                try:
                    logging.info(f"Trying Subscene variant: {variant}")
                    subs = subtitle.search(
                        title=variant,
                        year=str(year) if year else None,
                        language=language,
                        limit=str(min(limit, 20))
                    )

                    variant_results = flatten_results(subs, language)
                    if variant_results:
                        logging.info(f"Found {len(variant_results)} results for variant: {variant}")
                        all_results.extend(variant_results)
                        break  # Stop on first successful variant

                except Exception as e:
                    logging.debug(f"Variant '{variant}' failed: {e}")
                    continue

            return all_results[:limit]

        except Exception as e:
            logging.error(f"Alternative Subscene search error: {e}")
            return []


class YifySubtitlesSource(SubtitleSource):
    """YIFY Subtitles source - disabled to avoid 404 errors."""

    @property
    def name(self) -> str:
        return "YIFY Subtitles"

    def search(self, query: str, year: Optional[int] = None,
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search YIFY Subtitles (disabled for now)."""
        try:
            # Return empty results to avoid fake download links
            logging.info(f"YIFY search disabled for: {query} (real implementation needed)")
            return []

        except Exception as e:
            logging.error(f"YIFY search error: {e}")
            return []


class MultiSourceSearcher:
    """Search multiple subtitle sources."""
    
    def __init__(self, sources: Optional[List[SubtitleSource]] = None):
        """Initialize with subtitle sources."""
        if sources is None:
            sources = []

            # Add real OpenSubtitles integration first (highest priority)
            try:
                from opensubtitles_real import OpenSubtitlesRealSource
                sources.append(OpenSubtitlesRealSource())
                logging.info("Added real OpenSubtitles integration")
            except ImportError:
                logging.debug("Real OpenSubtitles integration not available")

            # Add Subscene sources
            sources.extend([
                SubsceneSource(),
                AlternativeSubsceneSource(),
            ])

            # Add enhanced Subscene as fallback
            try:
                from subscene_enhanced import SubsceneEnhancedSource
                sources.append(SubsceneEnhancedSource())
                logging.info("Added enhanced Subscene integration")
            except ImportError:
                logging.debug("Enhanced Subscene integration not available")

        self.sources = sources
    
    def search_all(self, query: str, year: Optional[int] = None, 
                   language: str = "english", limit: int = 50) -> Dict[str, List[Dict[str, Any]]]:
        """Search all sources and return results grouped by source."""
        results = {}
        
        for source in self.sources:
            try:
                logging.info(f"Searching {source.name} for: {query}")
                source_results = source.search(query, year, language, limit)
                results[source.name] = source_results
                logging.info(f"Found {len(source_results)} results from {source.name}")
                
            except Exception as e:
                logging.error(f"Error searching {source.name}: {e}")
                results[source.name] = []
        
        return results
    
    def search_combined(self, query: str, year: Optional[int] = None, 
                       language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search all sources and return combined results."""
        all_results = []
        source_results = self.search_all(query, year, language, limit)
        
        for source_name, results in source_results.items():
            for result in results:
                result["source"] = source_name
                all_results.append(result)
        
        # Sort by source priority and download count if available
        def sort_key(item):
            source_priority = {"Subscene": 0, "OpenSubtitles": 1, "YIFY Subtitles": 2}
            downloads = item.get("downloads", 0)
            return (source_priority.get(item.get("source", ""), 999), -downloads)
        
        all_results.sort(key=sort_key)
        return all_results[:limit]


# Convenience function for backward compatibility
def search_multiple_sources(query: str, year: Optional[int] = None, 
                          language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
    """Search multiple subtitle sources."""
    searcher = MultiSourceSearcher()
    return searcher.search_combined(query, year, language, limit)
