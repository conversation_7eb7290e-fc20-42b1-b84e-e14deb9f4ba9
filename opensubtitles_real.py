"""Real OpenSubtitles integration using web scraping."""

import logging
import requests
import re
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, quote, unquote
from bs4 import BeautifulSoup
import config
from subtitle_sources import SubtitleSource


class OpenSubtitlesRealSource(SubtitleSource):
    """Real OpenSubtitles source using web scraping."""
    
    BASE_URL = "https://www.opensubtitles.org"
    
    @property
    def name(self) -> str:
        return "OpenSubtitles"
    
    def __init__(self):
        """Initialize with session for cookie persistence."""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def _get_language_code(self, language: str) -> str:
        """Convert language name to OpenSubtitles language code."""
        lang_map = {
            "english": "eng",
            "spanish": "spa", 
            "french": "fre",
            "german": "ger",
            "italian": "ita",
            "portuguese": "por",
            "russian": "rus",
            "chinese": "chi",
            "japanese": "jpn",
            "korean": "kor",
            "arabic": "ara",
            "dutch": "dut",
            "swedish": "swe",
            "norwegian": "nor",
            "danish": "dan",
            "finnish": "fin"
        }
        return lang_map.get(language.lower(), "eng")
    
    def search(self, query: str, year: Optional[int] = None, 
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search OpenSubtitles using web scraping."""
        try:
            # Prepare search parameters
            lang_code = self._get_language_code(language)
            search_query = quote(query)
            
            # Build search URL
            search_url = f"{self.BASE_URL}/en/search/sublanguageid-{lang_code}/moviename-{search_query}"
            if year:
                search_url += f"/movieyear-{year}"
            
            logging.info(f"Searching OpenSubtitles: {search_url}")
            
            # Make request
            response = self.session.get(search_url, timeout=config.MAX_DOWNLOAD_TIMEOUT)
            response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Find subtitle entries in the search results
            # OpenSubtitles uses a table structure for search results
            subtitle_table = soup.find('table', {'id': 'search_results'})
            if not subtitle_table:
                # Try alternative selectors
                subtitle_rows = soup.find_all('tr', class_=re.compile(r'(change|odd|even)'))
            else:
                subtitle_rows = subtitle_table.find_all('tr')[1:]  # Skip header row
            
            for row in subtitle_rows[:limit]:
                try:
                    # Extract subtitle information from table row
                    cells = row.find_all('td')
                    if len(cells) < 5:
                        continue
                    
                    # Find the download link
                    download_link = None
                    title_cell = cells[0] if cells else None
                    
                    if title_cell:
                        # Look for subtitle link
                        link_elem = title_cell.find('a', href=re.compile(r'/en/subtitles/'))
                        if link_elem:
                            subtitle_page_url = urljoin(self.BASE_URL, link_elem['href'])
                            title = link_elem.get_text(strip=True)
                            
                            # Get the actual download link
                            download_url = self._get_download_link(subtitle_page_url)
                            
                            if download_url and title:
                                # Extract additional info
                                downloads = self._extract_download_count(cells)
                                rating = self._extract_rating(cells)
                                
                                results.append({
                                    "name": title,
                                    "link": download_url,
                                    "language": language,
                                    "source": self.name,
                                    "downloads": downloads,
                                    "rating": rating,
                                    "page_url": subtitle_page_url
                                })
                
                except Exception as e:
                    logging.debug(f"Error parsing subtitle row: {e}")
                    continue
            
            logging.info(f"OpenSubtitles found {len(results)} results for: {query}")
            return results
            
        except Exception as e:
            logging.error(f"OpenSubtitles search error: {e}")
            return []
    
    def _get_download_link(self, subtitle_page_url: str) -> Optional[str]:
        """Extract the actual download link from a subtitle page."""
        try:
            # Add delay to be respectful
            time.sleep(1)
            
            response = self.session.get(subtitle_page_url, timeout=config.MAX_DOWNLOAD_TIMEOUT)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for download button/link
            download_patterns = [
                {'class': re.compile(r'download')},
                {'id': re.compile(r'download')},
                {'href': re.compile(r'/download/')},
                {'href': re.compile(r'\.srt$')},
                {'href': re.compile(r'\.zip$')}
            ]
            
            for pattern in download_patterns:
                download_elem = soup.find('a', pattern)
                if download_elem and download_elem.get('href'):
                    download_url = urljoin(self.BASE_URL, download_elem['href'])
                    logging.debug(f"Found download link: {download_url}")
                    return download_url
            
            # Alternative: look for any link containing "download"
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link['href']
                if 'download' in href.lower() or href.endswith('.srt') or href.endswith('.zip'):
                    download_url = urljoin(self.BASE_URL, href)
                    logging.debug(f"Found alternative download link: {download_url}")
                    return download_url
            
            logging.warning(f"No download link found on page: {subtitle_page_url}")
            return None
            
        except Exception as e:
            logging.error(f"Error getting download link from {subtitle_page_url}: {e}")
            return None
    
    def _extract_download_count(self, cells) -> int:
        """Extract download count from table cells."""
        try:
            for cell in cells:
                text = cell.get_text(strip=True)
                # Look for numbers that might be download counts
                numbers = re.findall(r'\d+', text)
                if numbers:
                    return int(numbers[0])
            return 0
        except:
            return 0
    
    def _extract_rating(self, cells) -> float:
        """Extract rating from table cells."""
        try:
            for cell in cells:
                # Look for rating indicators (stars, numbers with decimals)
                rating_match = re.search(r'(\d+\.?\d*)/?\d*', cell.get_text())
                if rating_match:
                    return float(rating_match.group(1))
            return 0.0
        except:
            return 0.0


def test_opensubtitles_real():
    """Test the real OpenSubtitles integration."""
    print("Testing Real OpenSubtitles Integration")
    print("=" * 50)
    
    source = OpenSubtitlesRealSource()
    
    # Test with a popular movie first
    test_queries = [
        ("The Matrix", 1999),
        ("11.22.63", 2016),
        ("Breaking Bad", None)
    ]
    
    for query, year in test_queries:
        print(f"\nSearching for: {query} ({year if year else 'no year'})")
        try:
            results = source.search(query, year=year, language="english", limit=3)
            print(f"Found {len(results)} results:")
            
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result['name']}")
                print(f"     Downloads: {result['downloads']}")
                print(f"     Link: {result['link']}")
                print(f"     Page: {result['page_url']}")
                print()
                
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    test_opensubtitles_real()
