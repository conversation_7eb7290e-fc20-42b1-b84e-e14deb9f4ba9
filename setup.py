#!/usr/bin/env python3
"""Setup script for Subtitle Finder application."""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        # Separate main requirements from dev requirements
        main_requirements = []
        for req in requirements:
            if req in ['pytest>=7.0.0', 'black>=22.0.0', 'flake8>=4.0.0', 'mypy>=0.950']:
                continue  # Skip dev dependencies
            main_requirements.append(req)
else:
    main_requirements = ['requests>=2.23.0,<3.0.0', 'subsceneAPI==0.2']

setup(
    name="subtitle-finder",
    version="2.0.0",
    description="A secure GUI application for searching and downloading subtitles from Subscene",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Subtitle Finder Team",
    author_email="",
    url="",
    packages=find_packages(),
    py_modules=[
        'subtitle_finder',
        'config',
        'utils',
        'downloader'
    ],
    install_requires=main_requirements,
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'black>=22.0.0',
            'flake8>=4.0.0',
            'mypy>=0.950',
            'coverage>=6.0.0'
        ]
    },
    python_requires='>=3.6',
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: End Users/Desktop',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Multimedia :: Video',
        'Topic :: Internet :: WWW/HTTP :: Dynamic Content',
    ],
    keywords='subtitle, download, subscene, gui, tkinter',
    entry_points={
        'console_scripts': [
            'subtitle-finder=subtitle_finder:main',
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
