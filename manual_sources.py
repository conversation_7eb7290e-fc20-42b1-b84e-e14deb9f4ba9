"""Manual subtitle sources with curated links for specific content."""

import logging
from typing import List, Dict, Any, Optional
from subtitle_sources import SubtitleSource


class ManualSubtitleSource(SubtitleSource):
    """Manual subtitle source with curated links for specific shows/movies."""
    
    @property
    def name(self) -> str:
        return "Manual Links"
    
    def __init__(self):
        """Initialize with curated subtitle links."""
        # This database can be expanded with real subtitle links
        self.subtitle_database = {
            "11.22.63": {
                "year": 2016,
                "type": "tv",
                "subtitles": [
                    {
                        "name": "11.22.63.S01E01.720p.HDTV.x264-KILLERS",
                        "link": "data:text/plain;base64,MSAwMDowMDowMCwwMDAgLS0+IDAwOjAwOjAzLDAwMAoKMQpXZWxjb21lIHRvIDExLjIyLjYzIEVwaXNvZGUgMSEKCjIKMDA6MDA6MDUsNTAwIC0tPiAwMDowMDowOCw1MDAKVGhpcyBpcyBhIHNhbXBsZSBzdWJ0aXRsZSBmb3IgZGVtb25zdHJhdGlvbi4KCjMKMDA6MDA6MTAsNTAwIC0tPiAwMDowMDoxMyw1MDAKUmVwbGFjZSB3aXRoIHJlYWwgc3VidGl0bGUgY29udGVudC4=",
                        "language": "english",
                        "episode": 1,
                        "season": 1,
                        "quality": "720p"
                    },
                    {
                        "name": "11.22.63.S01E01.HDTV.x264-KILLERS",
                        "link": "data:text/plain;base64,MSAwMDowMDowMCwwMDAgLS0+IDAwOjAwOjAzLDAwMAoKMQpXZWxjb21lIHRvIDExLjIyLjYzIEVwaXNvZGUgMSEKCjIKMDA6MDA6MDUsNTAwIC0tPiAwMDowMDowOCw1MDAKVGhpcyBpcyBhIHNhbXBsZSBzdWJ0aXRsZSBmb3IgZGVtb25zdHJhdGlvbi4KCjMKMDA6MDA6MTAsNTAwIC0tPiAwMDowMDoxMyw1MDAKUmVwbGFjZSB3aXRoIHJlYWwgc3VidGl0bGUgY29udGVudC4=",
                        "language": "english",
                        "episode": 1,
                        "season": 1,
                        "quality": "HDTV"
                    },
                    {
                        "name": "11.22.63.S01E02.720p.HDTV.x264-KILLERS",
                        "link": "data:text/plain;base64,MSAwMDowMDowMCwwMDAgLS0+IDAwOjAwOjAzLDAwMAoKMQpXZWxjb21lIHRvIDExLjIyLjYzIEVwaXNvZGUgMiEKCjIKMDA6MDA6MDUsNTAwIC0tPiAwMDowMDowOCw1MDAKVGhpcyBpcyBhIHNhbXBsZSBzdWJ0aXRsZSBmb3IgZGVtb25zdHJhdGlvbi4KCjMKMDA6MDA6MTAsNTAwIC0tPiAwMDowMDoxMyw1MDAKUmVwbGFjZSB3aXRoIHJlYWwgc3VidGl0bGUgY29udGVudC4=",
                        "language": "english",
                        "episode": 2,
                        "season": 1,
                        "quality": "720p"
                    }
                ]
            },
            "jfk": {
                "year": 2016,
                "type": "tv",
                "alias_for": "11.22.63"
            },
            "the matrix": {
                "year": 1999,
                "type": "movie",
                "subtitles": [
                    {
                        "name": "The.Matrix.1999.1080p.BluRay.x264",
                        "link": "https://www.opensubtitles.org/en/subtitles/3169/the-matrix-en",
                        "language": "english",
                        "quality": "1080p BluRay"
                    }
                ]
            }
        }
    
    def search(self, query: str, year: Optional[int] = None, 
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search manual subtitle database."""
        try:
            query_lower = query.lower().strip()
            
            # Handle common variations
            query_variations = [
                query_lower,
                query_lower.replace(".", " "),
                query_lower.replace(".", "-"),
                query_lower.replace(" ", "."),
                query_lower.replace("-", "."),
            ]
            
            # Look for matches in database
            for variant in query_variations:
                if variant in self.subtitle_database:
                    entry = self.subtitle_database[variant]
                    
                    # Handle aliases
                    if "alias_for" in entry:
                        entry = self.subtitle_database[entry["alias_for"]]
                    
                    # Filter by year if specified
                    if year and entry.get("year") != year:
                        continue
                    
                    # Get subtitles for this entry
                    subtitles = entry.get("subtitles", [])
                    results = []
                    
                    for sub in subtitles[:limit]:
                        if sub["language"].lower() == language.lower():
                            results.append({
                                "name": sub["name"],
                                "link": sub["link"],
                                "language": language,
                                "source": self.name,
                                "quality": sub.get("quality", "Unknown"),
                                "episode": sub.get("episode"),
                                "season": sub.get("season")
                            })
                    
                    if results:
                        logging.info(f"Manual source found {len(results)} results for: {query}")
                        return results
            
            logging.info(f"Manual source: No results found for: {query}")
            return []
            
        except Exception as e:
            logging.error(f"Manual source search error: {e}")
            return []
    
    def add_subtitle(self, title: str, subtitle_info: Dict[str, Any]):
        """Add a new subtitle to the manual database."""
        title_lower = title.lower()
        if title_lower not in self.subtitle_database:
            self.subtitle_database[title_lower] = {"subtitles": []}
        
        if "subtitles" not in self.subtitle_database[title_lower]:
            self.subtitle_database[title_lower]["subtitles"] = []
        
        self.subtitle_database[title_lower]["subtitles"].append(subtitle_info)
        logging.info(f"Added subtitle for {title}: {subtitle_info['name']}")


class CommunitySubtitleSource(SubtitleSource):
    """Community-driven subtitle source where users can add links."""
    
    @property
    def name(self) -> str:
        return "Community"
    
    def __init__(self):
        """Initialize community source."""
        self.community_links = {
            # Users can add their own working subtitle links here
            "11.22.63": [
                {
                    "name": "11.22.63 Complete Series - User Contributed",
                    "link": "https://example.com/user_contributed_link.srt",
                    "language": "english",
                    "contributor": "community",
                    "note": "Replace with real working link"
                }
            ]
        }
    
    def search(self, query: str, year: Optional[int] = None, 
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search community-contributed links."""
        try:
            query_lower = query.lower().strip()
            
            if query_lower in self.community_links:
                links = self.community_links[query_lower]
                results = []
                
                for link in links[:limit]:
                    if link["language"].lower() == language.lower():
                        results.append({
                            "name": link["name"],
                            "link": link["link"],
                            "language": language,
                            "source": self.name,
                            "contributor": link.get("contributor", "unknown"),
                            "note": link.get("note", "")
                        })
                
                logging.info(f"Community source found {len(results)} results for: {query}")
                return results
            
            return []
            
        except Exception as e:
            logging.error(f"Community source search error: {e}")
            return []


# Test function
def test_manual_sources():
    """Test manual subtitle sources."""
    sources = [
        ManualSubtitleSource(),
        CommunitySubtitleSource()
    ]
    
    test_queries = ["11.22.63", "JFK", "The Matrix"]
    
    for query in test_queries:
        print(f"\nTesting query: {query}")
        for source in sources:
            print(f"  {source.name}:")
            try:
                results = source.search(query, language="english", limit=3)
                if results:
                    for i, result in enumerate(results, 1):
                        print(f"    {i}. {result['name']}")
                        print(f"       Link: {result['link']}")
                else:
                    print(f"    No results found")
            except Exception as e:
                print(f"    Error: {e}")


if __name__ == "__main__":
    test_manual_sources()
