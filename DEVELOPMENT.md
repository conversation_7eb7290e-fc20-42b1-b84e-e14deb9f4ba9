# Development Guide

This guide provides information for developers who want to contribute to or modify the Subtitle Finder application.

## Architecture Overview

The application follows a modular architecture with clear separation of concerns:

### Core Modules

1. **`subtitle_finder.py`** - Main GUI application
   - Tkinter-based user interface
   - Event handling and user interactions
   - Threading for non-blocking operations

2. **`config.py`** - Configuration management
   - Application settings and constants
   - Default values and limits
   - Easy configuration updates

3. **`utils.py`** - Utility functions
   - Input validation functions
   - File name sanitization
   - URL validation and security checks
   - ZIP file extraction with safety checks

4. **`downloader.py`** - Download functionality
   - HTTP request handling
   - File download with progress tracking
   - Security validations and size limits

### Test Modules

1. **`test_utils.py`** - Tests for utility functions
2. **`test_downloader.py`** - Tests for download functionality
3. **`run_tests.py`** - Test runner and reporting

## Security Considerations

### Input Validation
- All user inputs are validated before processing
- Numeric inputs have range checks
- String inputs have length limits
- File paths are validated for safety

### Download Security
- URL validation prevents SSRF attacks
- File size limits prevent DoS attacks
- Path traversal protection in ZIP extraction
- Content type validation

### Error Handling
- Comprehensive exception handling
- User-friendly error messages
- Logging for debugging
- Graceful degradation

## Code Style Guidelines

### Python Style
- Follow PEP 8 style guidelines
- Use type hints for function parameters and return values
- Write docstrings for all functions and classes
- Keep functions focused and small

### Naming Conventions
- Use descriptive variable and function names
- Use snake_case for functions and variables
- Use UPPER_CASE for constants
- Use CamelCase for classes

### Documentation
- Document all public functions
- Include examples in docstrings
- Keep README.md updated
- Comment complex logic

## Testing Strategy

### Unit Tests
- Test all utility functions
- Test download functionality
- Mock external dependencies
- Test error conditions

### Integration Tests
- Test GUI components (future enhancement)
- Test end-to-end workflows
- Test with real network requests (optional)

### Test Coverage
- Aim for >90% code coverage
- Test both success and failure paths
- Test edge cases and boundary conditions

## Development Workflow

### Setting Up Development Environment

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd subtitle-finder
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -e .[dev]  # Install in development mode with dev dependencies
   ```

### Running Tests

```bash
# Run all tests
python run_tests.py

# Run specific test file
python -m unittest test_utils.py -v

# Run with coverage
coverage run run_tests.py
coverage report
coverage html  # Generate HTML report
```

### Code Quality Checks

```bash
# Format code
black *.py

# Check style
flake8 *.py

# Type checking
mypy *.py

# Run all quality checks
./quality_check.sh  # If you create this script
```

### Making Changes

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the coding guidelines
   - Add tests for new functionality
   - Update documentation

3. **Test your changes**
   ```bash
   python run_tests.py
   black *.py
   flake8 *.py
   ```

4. **Commit and push**
   ```bash
   git add .
   git commit -m "Add feature: description"
   git push origin feature/your-feature-name
   ```

## Adding New Features

### Adding a New Utility Function

1. Add the function to `utils.py`
2. Add configuration constants to `config.py` if needed
3. Write tests in `test_utils.py`
4. Update documentation

Example:
```python
# In utils.py
def new_validation_function(input_value: str) -> bool:
    """Validate input according to new rules."""
    # Implementation here
    pass

# In test_utils.py
class TestNewValidation(unittest.TestCase):
    def test_valid_input(self):
        self.assertTrue(utils.new_validation_function("valid"))
    
    def test_invalid_input(self):
        self.assertFalse(utils.new_validation_function("invalid"))
```

### Adding GUI Features

1. Modify the `create_gui()` function in `subtitle_finder.py`
2. Add event handlers for new widgets
3. Update validation logic if needed
4. Test the new functionality

### Adding Download Features

1. Modify `downloader.py`
2. Add new configuration options to `config.py`
3. Write comprehensive tests
4. Update error handling

## Performance Considerations

### GUI Responsiveness
- Use threading for long-running operations
- Update GUI from main thread only
- Provide progress feedback to users

### Memory Usage
- Stream large downloads instead of loading into memory
- Clean up temporary files
- Limit concurrent downloads

### Network Efficiency
- Implement proper timeout handling
- Add retry logic for transient failures
- Respect rate limiting

## Debugging

### Common Issues

1. **Import Errors**
   - Check Python path
   - Verify all dependencies are installed
   - Check for circular imports

2. **GUI Issues**
   - Test on different platforms
   - Check tkinter installation
   - Verify thread safety

3. **Network Issues**
   - Test with different URLs
   - Check firewall settings
   - Verify SSL certificates

### Debugging Tools

```python
# Add logging for debugging
import logging
logging.basicConfig(level=logging.DEBUG)

# Use pdb for interactive debugging
import pdb; pdb.set_trace()

# Print debug information
print(f"Debug: {variable_name}")
```

## Release Process

1. **Update version numbers**
   - `setup.py`
   - `README.md` changelog

2. **Run full test suite**
   ```bash
   python run_tests.py
   ```

3. **Update documentation**
   - README.md
   - DEVELOPMENT.md
   - Docstrings

4. **Create release**
   - Tag the release
   - Create release notes
   - Build distribution packages

## Future Enhancements

### Planned Features
- Configuration file support
- Plugin system for different subtitle sources
- Advanced search filters
- Subtitle preview functionality
- Batch processing from file lists

### Technical Improvements
- Async/await for better concurrency
- Database for caching search results
- REST API for programmatic access
- Web interface option

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following these guidelines
4. Add tests and documentation
5. Submit a pull request

### Pull Request Guidelines
- Include a clear description of changes
- Reference any related issues
- Ensure all tests pass
- Update documentation as needed
- Follow the code style guidelines
