"""Download functionality for Subtitle Finder application."""

import re
import zipfile
from pathlib import Path
from typing import Optional
import requests
import config
from utils import validate_url, safe_extract_zip, sanitize_filename
import base64
import urllib.parse


def _handle_data_url(data_url: str, dest_dir: Path, preferred_name: Optional[str] = None) -> Path:
    """Handle data URLs with base64 encoded content."""
    try:
        dest_dir.mkdir(parents=True, exist_ok=True)

        # Parse data URL: data:text/plain;base64,<base64_content>
        if not data_url.startswith("data:"):
            raise ValueError("Invalid data URL")

        # Extract the base64 content
        header, content = data_url.split(",", 1)
        if "base64" not in header:
            raise ValueError("Only base64 data URLs are supported")

        # Decode base64 content
        decoded_content = base64.b64decode(content)

        # Determine filename
        if preferred_name:
            filename = sanitize_filename(preferred_name)
        else:
            filename = "subtitle.srt"

        if not filename.lower().endswith((".srt", ".sub", ".vtt")):
            filename += ".srt"

        # Save file
        output_path = dest_dir / filename
        output_path.write_bytes(decoded_content)

        return output_path

    except Exception as e:
        raise ValueError(f"Error processing data URL: {str(e)}")


def fetch_and_save(url: str, dest_dir: Path, preferred_name: Optional[str] = None) -> Path:
    """Download and save subtitle file with security checks."""
    # Handle data URLs (base64 encoded content)
    if url.startswith("data:"):
        return _handle_data_url(url, dest_dir, preferred_name)

    # Validate URL
    if not validate_url(url):
        raise ValueError(f"Invalid or unsafe URL: {url}")

    try:
        dest_dir.mkdir(parents=True, exist_ok=True)
        
        # Download with size limit
        response = requests.get(url, timeout=config.MAX_DOWNLOAD_TIMEOUT, stream=True)
        response.raise_for_status()
        
        # Check content length
        content_length = response.headers.get('content-length')
        if content_length and int(content_length) > config.MAX_FILE_SIZE:
            raise ValueError(f"File too large: {content_length} bytes")
        
        # Download content with size checking
        content = b""
        for chunk in response.iter_content(chunk_size=config.CHUNK_SIZE):
            content += chunk
            if len(content) > config.MAX_FILE_SIZE:
                raise ValueError(f"File too large during download: {len(content)} bytes")
        
        # Determine filename
        content_disposition = response.headers.get("Content-Disposition", "")
        filename_match = re.search(r'filename="?([^"]+)"?', content_disposition)
        if filename_match:
            filename = filename_match.group(1)
        else:
            filename = preferred_name or url.split("/")[-1] or "subtitle.zip"
        filename = sanitize_filename(filename)
        
        # Process content
        content_type = response.headers.get("Content-Type", "").lower()
        if filename.lower().endswith(config.ARCHIVE_EXTENSIONS) or content_type == "application/zip":
            return safe_extract_zip(content, dest_dir)
        else:
            # Handle non-ZIP files
            if not re.search(r"\.(srt|sub|vtt|txt)$", filename, flags=re.I):
                filename += ".srt"
            tmp_path = dest_dir / filename
            tmp_path.write_bytes(content)
            return tmp_path
            
    except requests.exceptions.RequestException as e:
        raise ValueError(f"Network error during download: {str(e)}")
    except zipfile.BadZipFile:
        raise ValueError("Invalid ZIP file received.")
    except Exception as e:
        raise ValueError(f"Error processing file: {str(e)}")
