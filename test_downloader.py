"""Unit tests for downloader module."""

import unittest
import tempfile
import zipfile
import io
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import requests
import downloader
import config


class TestFetchAndSave(unittest.TestCase):
    """Test the fetch_and_save function."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_mock_response(self, content, content_type="text/plain", filename=None):
        """Create a mock HTTP response."""
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.headers = {"Content-Type": content_type}
        mock_response.iter_content.return_value = [content[i:i+1024] for i in range(0, len(content), 1024)]
        
        if filename:
            mock_response.headers["Content-Disposition"] = f'attachment; filename="{filename}"'
        
        return mock_response
    
    def create_test_zip(self, files):
        """Create a test ZIP file with given files."""
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w') as zf:
            for filename, content in files.items():
                zf.writestr(filename, content)
        return zip_buffer.getvalue()
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_download_text_file(self, mock_validate_url, mock_get):
        """Test downloading a plain text subtitle file."""
        mock_validate_url.return_value = True
        content = b"Test subtitle content"
        mock_get.return_value = self.create_mock_response(content, filename="test.srt")
        
        result = downloader.fetch_and_save("http://example.com/test.srt", self.temp_dir)
        
        self.assertTrue(result.exists())
        self.assertEqual(result.read_bytes(), content)
        self.assertEqual(result.name, "test.srt")
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_download_zip_file(self, mock_validate_url, mock_get):
        """Test downloading and extracting a ZIP file."""
        mock_validate_url.return_value = True
        zip_content = self.create_test_zip({"subtitle.srt": "Test subtitle content"})
        mock_get.return_value = self.create_mock_response(
            zip_content, 
            content_type="application/zip",
            filename="subtitles.zip"
        )
        
        result = downloader.fetch_and_save("http://example.com/test.zip", self.temp_dir)
        
        self.assertTrue(result.exists())
        self.assertEqual(result.read_text(), "Test subtitle content")
        self.assertEqual(result.name, "subtitle.srt")
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_download_file_without_extension(self, mock_validate_url, mock_get):
        """Test downloading file without subtitle extension."""
        mock_validate_url.return_value = True
        content = b"Test subtitle content"
        mock_get.return_value = self.create_mock_response(content, filename="subtitle")
        
        result = downloader.fetch_and_save("http://example.com/subtitle", self.temp_dir)
        
        self.assertTrue(result.exists())
        self.assertEqual(result.read_bytes(), content)
        self.assertTrue(result.name.endswith(".srt"))
    
    @patch('downloader.validate_url')
    def test_invalid_url_rejected(self, mock_validate_url):
        """Test that invalid URLs are rejected."""
        mock_validate_url.return_value = False
        
        with self.assertRaises(ValueError) as cm:
            downloader.fetch_and_save("http://localhost/test.srt", self.temp_dir)
        
        self.assertIn("Invalid or unsafe URL", str(cm.exception))
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_network_error_handling(self, mock_validate_url, mock_get):
        """Test handling of network errors."""
        mock_validate_url.return_value = True
        mock_get.side_effect = requests.exceptions.RequestException("Network error")
        
        with self.assertRaises(ValueError) as cm:
            downloader.fetch_and_save("http://example.com/test.srt", self.temp_dir)
        
        self.assertIn("Network error during download", str(cm.exception))
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_file_too_large_content_length(self, mock_validate_url, mock_get):
        """Test rejection of files that are too large (via Content-Length)."""
        mock_validate_url.return_value = True
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.headers = {
            "Content-Length": str(config.MAX_FILE_SIZE + 1),
            "Content-Type": "text/plain"
        }
        mock_get.return_value = mock_response
        
        with self.assertRaises(ValueError) as cm:
            downloader.fetch_and_save("http://example.com/large.srt", self.temp_dir)
        
        self.assertIn("File too large", str(cm.exception))
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_file_too_large_during_download(self, mock_validate_url, mock_get):
        """Test rejection of files that are too large during download."""
        mock_validate_url.return_value = True
        large_content = b"x" * (config.MAX_FILE_SIZE + 1)
        mock_get.return_value = self.create_mock_response(large_content)
        
        with self.assertRaises(ValueError) as cm:
            downloader.fetch_and_save("http://example.com/large.srt", self.temp_dir)
        
        self.assertIn("File too large during download", str(cm.exception))
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_timeout_configuration(self, mock_validate_url, mock_get):
        """Test that timeout is properly configured."""
        mock_validate_url.return_value = True
        content = b"Test content"
        mock_get.return_value = self.create_mock_response(content)
        
        downloader.fetch_and_save("http://example.com/test.srt", self.temp_dir)
        
        # Verify that requests.get was called with the correct timeout
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        self.assertEqual(kwargs['timeout'], config.MAX_DOWNLOAD_TIMEOUT)
        self.assertTrue(kwargs['stream'])
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_preferred_name_used(self, mock_validate_url, mock_get):
        """Test that preferred name is used when provided."""
        mock_validate_url.return_value = True
        content = b"Test content"
        mock_response = self.create_mock_response(content)
        # Remove Content-Disposition header
        del mock_response.headers["Content-Disposition"]
        mock_get.return_value = mock_response
        
        result = downloader.fetch_and_save(
            "http://example.com/unknown", 
            self.temp_dir, 
            preferred_name="custom_name.srt"
        )
        
        self.assertTrue(result.exists())
        self.assertEqual(result.name, "custom_name.srt")
    
    @patch('downloader.requests.get')
    @patch('downloader.validate_url')
    def test_bad_zip_file_handling(self, mock_validate_url, mock_get):
        """Test handling of corrupted ZIP files."""
        mock_validate_url.return_value = True
        # Create invalid ZIP content
        invalid_zip = b"This is not a valid ZIP file"
        mock_get.return_value = self.create_mock_response(
            invalid_zip, 
            content_type="application/zip"
        )
        
        with self.assertRaises(ValueError) as cm:
            downloader.fetch_and_save("http://example.com/bad.zip", self.temp_dir)
        
        self.assertIn("Invalid ZIP file", str(cm.exception))


if __name__ == '__main__':
    unittest.main()
