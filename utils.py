"""Utility functions for Subtitle Finder application."""

import io
import re
import zipfile
import urllib.parse
from pathlib import Path
from typing import Dict, Any, List
import config


def sanitize_filename(name: str) -> str:
    """Sanitize filename by removing invalid characters."""
    if not name:
        return "subtitle"
    return re.sub(r'[\\/:*?"<>|]+', "_", name).strip()


def validate_url(url: str) -> bool:
    """Validate URL to prevent SSRF attacks."""
    try:
        parsed = urllib.parse.urlparse(url)
        # Only allow HTTP/HTTPS protocols
        if parsed.scheme not in ('http', 'https'):
            return False
        # Prevent localhost/private IP access
        hostname = parsed.hostname
        if hostname in ('localhost', '127.0.0.1', '0.0.0.0') or \
           hostname.startswith('192.168.') or \
           hostname.startswith('10.') or \
           hostname.startswith('172.'):
            return False
        return True
    except Exception:
        return False


def safe_extract_zip(zip_content: bytes, dest_dir: Path, max_size: int = None) -> Path:
    """Safely extract ZIP file with path traversal protection."""
    if max_size is None:
        max_size = config.MAX_FILE_SIZE
        
    if len(zip_content) > max_size:
        raise ValueError(f"ZIP file too large: {len(zip_content)} bytes")
    
    with zipfile.ZipFile(io.BytesIO(zip_content)) as zf:
        # Check for path traversal attacks
        for member in zf.namelist():
            if member.startswith('/') or '..' in member or '\\' in member:
                raise ValueError(f"Unsafe path in ZIP: {member}")
        
        # Find subtitle files
        subtitle_files = [n for n in zf.namelist() 
                         if n.lower().endswith(config.SUBTITLE_EXTENSIONS)]
        if not subtitle_files:
            # Fallback to first file if no subtitle files found
            if not zf.namelist():
                raise ValueError("Empty ZIP file")
            subtitle_files = [zf.namelist()[0]]
        
        # Extract the first subtitle file
        name = subtitle_files[0]
        data = zf.read(name)
        if len(data) > max_size:
            raise ValueError(f"Extracted file too large: {len(data)} bytes")
        
        final = dest_dir / sanitize_filename(Path(name).name)
        final.write_bytes(data)
        return final


def flatten_results(subs, language: str) -> List[Dict[str, Any]]:
    """Convert subscene results to a flat list of dictionaries."""
    rows: List[Dict[str, Any]] = []
    if not hasattr(subs, 'ZIPlinks'):
        return rows
    
    for name, link in subs.ZIPlinks:
        if name and link:  # Ensure both name and link are valid
            rows.append({
                "name": str(name).strip(),
                "link": str(link).strip(),
                "language": language
            })
    return rows


def validate_year(year_str: str) -> int:
    """Validate and parse year input."""
    if not year_str.isdigit() or len(year_str) != 4:
        raise ValueError("Year must be a 4-digit number.")
    year = int(year_str)
    if year < config.MIN_YEAR or year > config.MAX_YEAR:
        raise ValueError(f"Year must be between {config.MIN_YEAR} and {config.MAX_YEAR}.")
    return year


def validate_season(season_str: str) -> int:
    """Validate and parse season input."""
    if not season_str.isdigit():
        raise ValueError("Season must be a number.")
    season = int(season_str)
    if season < 1 or season > config.MAX_SEASON:
        raise ValueError(f"Season must be between 1 and {config.MAX_SEASON}.")
    return season


def validate_episode(episode_str: str) -> int:
    """Validate and parse episode input."""
    if not episode_str.isdigit():
        raise ValueError("Episode must be a number.")
    episode = int(episode_str)
    if episode < 1 or episode > config.MAX_EPISODE:
        raise ValueError(f"Episode must be between 1 and {config.MAX_EPISODE}.")
    return episode


def validate_limit(limit_str: str) -> int:
    """Validate and parse limit input."""
    if not limit_str.isdigit():
        raise ValueError("Limit must be a number.")
    limit = int(limit_str)
    if limit < config.MIN_SEARCH_LIMIT or limit > config.MAX_SEARCH_LIMIT:
        raise ValueError(f"Limit must be between {config.MIN_SEARCH_LIMIT} and {config.MAX_SEARCH_LIMIT}.")
    return limit
