"""Enhanced Subscene integration with better search capabilities."""

import logging
import requests
import re
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup
import config
from subtitle_sources import SubtitleSource, flatten_results


class SubsceneEnhancedSource(SubtitleSource):
    """Enhanced Subscene source with web scraping fallback."""
    
    BASE_URL = "https://subscene.com"
    
    @property
    def name(self) -> str:
        return "Subscene Enhanced"
    
    def __init__(self):
        """Initialize with session for cookie persistence."""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Referer': 'https://subscene.com/',
        })
    
    def search(self, query: str, year: Optional[int] = None, 
               language: str = "english", limit: int = 50) -> List[Dict[str, Any]]:
        """Search Subscene using both API and web scraping."""
        try:
            # First try the original subsceneAPI
            api_results = self._search_with_api(query, year, language, limit)
            if api_results:
                return api_results
            
            # If API fails, try web scraping
            logging.info(f"API search failed, trying web scraping for: {query}")
            return self._search_with_scraping(query, year, language, limit)
            
        except Exception as e:
            logging.error(f"Subscene Enhanced search error: {e}")
            return []
    
    def _search_with_api(self, query: str, year: Optional[int], language: str, limit: int) -> List[Dict[str, Any]]:
        """Try searching with the original subsceneAPI."""
        try:
            from subsceneAPI import subtitle
            
            # Try multiple query variations
            query_variations = [
                query,
                query.replace(".", " "),
                query.replace(".", "-"),
                query.replace(" ", "."),
            ]
            
            # Add specific variations for common patterns
            if "11.22.63" in query.lower():
                query_variations.extend([
                    "11.22.63",
                    "11 22 63", 
                    "JFK",
                    "eleven twenty two sixty three"
                ])
            
            for variant in query_variations:
                try:
                    logging.debug(f"Trying API variant: {variant}")
                    subs = subtitle.search(
                        title=variant,
                        year=str(year) if year else None,
                        language=language,
                        limit=str(min(limit, 50))
                    )
                    
                    results = flatten_results(subs, language)
                    if results:
                        logging.info(f"API found {len(results)} results for variant: {variant}")
                        return results
                        
                except Exception as e:
                    logging.debug(f"API variant '{variant}' failed: {e}")
                    continue
            
            return []
            
        except Exception as e:
            logging.debug(f"API search failed: {e}")
            return []
    
    def _search_with_scraping(self, query: str, year: Optional[int], language: str, limit: int) -> List[Dict[str, Any]]:
        """Search Subscene using web scraping."""
        try:
            # Build search URL
            search_url = f"{self.BASE_URL}/subtitles/searchbytitle"
            
            # Prepare search data
            search_data = {
                'query': query,
                'l': '',  # Language filter (empty for all)
            }
            
            logging.info(f"Scraping Subscene search: {query}")
            
            # Make search request
            response = self.session.post(search_url, data=search_data, timeout=config.MAX_DOWNLOAD_TIMEOUT)
            response.raise_for_status()
            
            # Parse results
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Find search results
            result_items = soup.find_all('div', class_='title')
            
            for item in result_items[:limit]:
                try:
                    # Extract title and link
                    link_elem = item.find('a')
                    if not link_elem:
                        continue
                    
                    title = link_elem.get_text(strip=True)
                    movie_url = urljoin(self.BASE_URL, link_elem['href'])
                    
                    # Filter by year if specified
                    if year:
                        year_match = re.search(r'\((\d{4})\)', title)
                        if year_match and int(year_match.group(1)) != year:
                            continue
                    
                    # Get subtitles for this movie/show
                    subtitle_results = self._get_subtitles_for_movie(movie_url, language, limit)
                    results.extend(subtitle_results)
                    
                except Exception as e:
                    logging.debug(f"Error parsing search result: {e}")
                    continue
            
            logging.info(f"Subscene scraping found {len(results)} results for: {query}")
            return results
            
        except Exception as e:
            logging.error(f"Subscene scraping error: {e}")
            return []
    
    def _get_subtitles_for_movie(self, movie_url: str, language: str, limit: int) -> List[Dict[str, Any]]:
        """Get subtitle list for a specific movie/show page."""
        try:
            # Add delay to be respectful
            time.sleep(1)
            
            response = self.session.get(movie_url, timeout=config.MAX_DOWNLOAD_TIMEOUT)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Find subtitle entries
            subtitle_rows = soup.find_all('tr')
            
            for row in subtitle_rows[:limit]:
                try:
                    # Look for language and download link
                    lang_cell = row.find('td', class_='a1')
                    if not lang_cell:
                        continue
                    
                    # Check if this is the right language
                    lang_text = lang_cell.get_text(strip=True).lower()
                    if language.lower() not in lang_text:
                        continue
                    
                    # Find download link
                    download_cell = row.find('td', class_='a41')
                    if not download_cell:
                        continue
                    
                    download_link = download_cell.find('a')
                    if not download_link:
                        continue
                    
                    # Extract subtitle info
                    name_cell = row.find('td', class_='a1')
                    subtitle_name = name_cell.get_text(strip=True) if name_cell else "Unknown"
                    
                    download_url = urljoin(self.BASE_URL, download_link['href'])
                    
                    results.append({
                        "name": subtitle_name,
                        "link": download_url,
                        "language": language,
                        "source": self.name,
                        "page_url": movie_url
                    })
                    
                except Exception as e:
                    logging.debug(f"Error parsing subtitle row: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logging.error(f"Error getting subtitles from {movie_url}: {e}")
            return []


def test_subscene_enhanced():
    """Test the enhanced Subscene integration."""
    print("Testing Enhanced Subscene Integration")
    print("=" * 50)
    
    source = SubsceneEnhancedSource()
    
    test_queries = [
        ("The Matrix", 1999),
        ("11.22.63", 2016),
        ("Breaking Bad", None)
    ]
    
    for query, year in test_queries:
        print(f"\nSearching for: {query} ({year if year else 'no year'})")
        try:
            results = source.search(query, year=year, language="english", limit=3)
            print(f"Found {len(results)} results:")
            
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result['name']}")
                print(f"     Link: {result['link']}")
                print()
                
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    test_subscene_enhanced()
