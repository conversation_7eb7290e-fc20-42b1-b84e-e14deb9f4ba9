#!/usr/bin/env python3
"""Test real subtitle download from OpenSubtitles."""

import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_real_opensubtitles_download():
    """Test downloading a real subtitle from OpenSubtitles."""
    try:
        from subtitle_sources import MultiSourceSearcher
        from downloader import fetch_and_save
        
        print("Testing Real OpenSubtitles Download")
        print("=" * 50)
        
        # Search for subtitles
        searcher = MultiSourceSearcher()
        results = searcher.search_combined("11.22.63", year=2016, language="english", limit=5)
        
        if not results:
            print("❌ No results found")
            return False
        
        print(f"✅ Found {len(results)} results")
        
        # Find OpenSubtitles results (real downloads)
        opensubtitles_results = [r for r in results if r.get('source') == 'OpenSubtitles']
        
        if not opensubtitles_results:
            print("❌ No OpenSubtitles results found")
            return False
        
        print(f"✅ Found {len(opensubtitles_results)} OpenSubtitles results")
        
        # Try to download the first OpenSubtitles result
        first_result = opensubtitles_results[0]
        print(f"\nDownloading: {first_result['name']}")
        print(f"From: {first_result['source']}")
        print(f"URL: {first_result['link']}")
        
        output_dir = Path("./real_subtitles")
        output_dir.mkdir(exist_ok=True)
        
        try:
            downloaded_file = fetch_and_save(
                first_result['link'], 
                output_dir, 
                "11.22.63.real.srt"
            )
            
            print(f"✅ Downloaded successfully to: {downloaded_file}")
            print(f"File size: {downloaded_file.stat().st_size} bytes")
            
            # Show first few lines of the real subtitle
            content = downloaded_file.read_text(encoding='utf-8', errors='ignore')
            lines = content.split('\n')[:10]
            print(f"\nFirst 10 lines of real subtitle:")
            print("-" * 40)
            for i, line in enumerate(lines, 1):
                print(f"{i:2d}: {line}")
            print("-" * 40)
            
            # Check if it looks like a real subtitle
            has_timestamps = any('-->' in line for line in lines)
            has_numbers = any(line.strip().isdigit() for line in lines)
            
            if has_timestamps and has_numbers:
                print("✅ File appears to be a valid SRT subtitle!")
            else:
                print("⚠️  File may not be a valid SRT subtitle")
            
            return True
            
        except Exception as e:
            print(f"❌ Download failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_sources():
    """Test all available sources."""
    try:
        from subtitle_sources import MultiSourceSearcher
        
        print("\nTesting All Sources")
        print("=" * 50)
        
        searcher = MultiSourceSearcher()
        print(f"Available sources: {[s.name for s in searcher.sources]}")
        
        # Test search
        results = searcher.search_all("11.22.63", year=2016, language="english", limit=3)
        
        print(f"\nResults by source:")
        for source_name, source_results in results.items():
            print(f"\n{source_name}: {len(source_results)} results")
            for i, result in enumerate(source_results[:2], 1):
                print(f"  {i}. {result.get('name', 'Unknown')}")
                print(f"     Link: {result.get('link', 'No link')[:60]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Source test error: {e}")
        return False

if __name__ == "__main__":
    print("Real Subtitle Download Test")
    print("=" * 60)
    
    success1 = test_all_sources()
    success2 = test_real_opensubtitles_download()
    
    if success1 and success2:
        print("\n🎉 All tests passed!")
        print("Real subtitle download is working!")
    else:
        print("\n❌ Some tests failed!")
        print("Check the error messages above.")
