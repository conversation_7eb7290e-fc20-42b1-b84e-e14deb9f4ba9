#!/usr/bin/env python3
"""Test script for multi-source subtitle search."""

import sys
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_multisource_search():
    """Test the multi-source search functionality."""
    try:
        from subtitle_sources import MultiSourceSearcher
        
        print("Testing Multi-Source Subtitle Search")
        print("=" * 40)
        
        # Create searcher
        searcher = MultiSourceSearcher()
        print(f"Initialized searcher with {len(searcher.sources)} sources:")
        for source in searcher.sources:
            print(f"  - {source.name}")
        
        # Test search for 11.22.63
        print(f"\nSearching for '11.22.63'...")
        results = searcher.search_all("11.22.63", year=2016, language="english", limit=10)
        
        print(f"\nResults by source:")
        total_results = 0
        for source_name, source_results in results.items():
            print(f"\n{source_name}: {len(source_results)} results")
            total_results += len(source_results)
            
            for i, result in enumerate(source_results[:3], 1):  # Show first 3
                print(f"  {i}. {result.get('name', 'Unknown')}")
                print(f"     Link: {result.get('link', 'No link')}")
                if 'downloads' in result:
                    print(f"     Downloads: {result['downloads']}")
        
        print(f"\nTotal results found: {total_results}")
        
        # Test combined search
        print(f"\nTesting combined search...")
        combined_results = searcher.search_combined("11.22.63", year=2016, language="english", limit=10)
        print(f"Combined results: {len(combined_results)}")
        
        for i, result in enumerate(combined_results[:5], 1):  # Show first 5
            print(f"  {i}. [{result.get('source', 'Unknown')}] {result.get('name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_sources():
    """Test individual sources."""
    try:
        from subtitle_sources import SubsceneSource, OpenSubtitlesWebSource, YifySubtitlesSource
        
        print("\nTesting Individual Sources")
        print("=" * 40)
        
        sources = [
            SubsceneSource(),
            OpenSubtitlesWebSource(), 
            YifySubtitlesSource()
        ]
        
        for source in sources:
            print(f"\nTesting {source.name}...")
            try:
                results = source.search("11.22.63", year=2016, language="english", limit=5)
                print(f"  Found {len(results)} results")
                
                for i, result in enumerate(results[:2], 1):  # Show first 2
                    print(f"    {i}. {result.get('name', 'Unknown')}")
                    
            except Exception as e:
                print(f"  Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"Error testing individual sources: {e}")
        return False

if __name__ == "__main__":
    print("Multi-Source Subtitle Search Test")
    print("=" * 50)
    
    success1 = test_individual_sources()
    success2 = test_multisource_search()
    
    if success1 and success2:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
