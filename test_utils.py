"""Unit tests for utility functions."""

import unittest
import tempfile
import zipfile
import io
from pathlib import Path
from unittest.mock import Mock, patch
import utils
import config


class TestSanitizeFilename(unittest.TestCase):
    """Test filename sanitization."""
    
    def test_sanitize_valid_filename(self):
        """Test sanitizing a valid filename."""
        result = utils.sanitize_filename("test_file.srt")
        self.assertEqual(result, "test_file.srt")
    
    def test_sanitize_invalid_characters(self):
        """Test sanitizing filename with invalid characters."""
        result = utils.sanitize_filename("test<>file|name?.srt")
        self.assertEqual(result, "test_file_name_.srt")
    
    def test_sanitize_empty_string(self):
        """Test sanitizing empty string."""
        result = utils.sanitize_filename("")
        self.assertEqual(result, "subtitle")
    
    def test_sanitize_none(self):
        """Test sanitizing None value."""
        result = utils.sanitize_filename(None)
        self.assertEqual(result, "subtitle")


class TestValidateUrl(unittest.TestCase):
    """Test URL validation."""
    
    def test_valid_http_url(self):
        """Test valid HTTP URL."""
        self.assertTrue(utils.validate_url("http://example.com/file.zip"))
    
    def test_valid_https_url(self):
        """Test valid HTTPS URL."""
        self.assertTrue(utils.validate_url("https://example.com/file.zip"))
    
    def test_invalid_protocol(self):
        """Test invalid protocol."""
        self.assertFalse(utils.validate_url("ftp://example.com/file.zip"))
        self.assertFalse(utils.validate_url("file:///etc/passwd"))
    
    def test_localhost_blocked(self):
        """Test localhost URLs are blocked."""
        self.assertFalse(utils.validate_url("http://localhost/file.zip"))
        self.assertFalse(utils.validate_url("http://127.0.0.1/file.zip"))
    
    def test_private_ip_blocked(self):
        """Test private IP addresses are blocked."""
        self.assertFalse(utils.validate_url("http://***********/file.zip"))
        self.assertFalse(utils.validate_url("http://********/file.zip"))
        self.assertFalse(utils.validate_url("http://**********/file.zip"))
    
    def test_malformed_url(self):
        """Test malformed URLs."""
        self.assertFalse(utils.validate_url("not-a-url"))
        self.assertFalse(utils.validate_url(""))


class TestSafeExtractZip(unittest.TestCase):
    """Test safe ZIP extraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_zip(self, files):
        """Create a test ZIP file with given files."""
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w') as zf:
            for filename, content in files.items():
                zf.writestr(filename, content)
        return zip_buffer.getvalue()
    
    def test_extract_valid_subtitle(self):
        """Test extracting valid subtitle file."""
        zip_content = self.create_test_zip({"test.srt": "Test subtitle content"})
        result = utils.safe_extract_zip(zip_content, self.temp_dir)
        
        self.assertTrue(result.exists())
        self.assertEqual(result.read_text(), "Test subtitle content")
        self.assertEqual(result.name, "test.srt")
    
    def test_extract_multiple_subtitles(self):
        """Test extracting from ZIP with multiple subtitle files."""
        zip_content = self.create_test_zip({
            "test1.srt": "First subtitle",
            "test2.sub": "Second subtitle"
        })
        result = utils.safe_extract_zip(zip_content, self.temp_dir)
        
        self.assertTrue(result.exists())
        # Should extract the first subtitle file found
        self.assertEqual(result.read_text(), "First subtitle")
    
    def test_path_traversal_protection(self):
        """Test protection against path traversal attacks."""
        zip_content = self.create_test_zip({"../../../etc/passwd": "malicious content"})
        
        with self.assertRaises(ValueError) as cm:
            utils.safe_extract_zip(zip_content, self.temp_dir)
        
        self.assertIn("Unsafe path", str(cm.exception))
    
    def test_file_too_large(self):
        """Test protection against large files."""
        large_content = "x" * (config.MAX_FILE_SIZE + 1)
        zip_content = self.create_test_zip({"test.srt": large_content})
        
        with self.assertRaises(ValueError) as cm:
            utils.safe_extract_zip(zip_content, self.temp_dir)
        
        self.assertIn("too large", str(cm.exception))
    
    def test_empty_zip(self):
        """Test handling of empty ZIP files."""
        zip_content = self.create_test_zip({})
        
        with self.assertRaises(ValueError) as cm:
            utils.safe_extract_zip(zip_content, self.temp_dir)
        
        self.assertIn("Empty ZIP file", str(cm.exception))


class TestValidationFunctions(unittest.TestCase):
    """Test input validation functions."""
    
    def test_validate_year_valid(self):
        """Test valid year validation."""
        self.assertEqual(utils.validate_year("2023"), 2023)
        self.assertEqual(utils.validate_year("1950"), 1950)
    
    def test_validate_year_invalid(self):
        """Test invalid year validation."""
        with self.assertRaises(ValueError):
            utils.validate_year("23")  # Too short
        with self.assertRaises(ValueError):
            utils.validate_year("1800")  # Too old
        with self.assertRaises(ValueError):
            utils.validate_year("2050")  # Too future
        with self.assertRaises(ValueError):
            utils.validate_year("abcd")  # Not a number
    
    def test_validate_season_valid(self):
        """Test valid season validation."""
        self.assertEqual(utils.validate_season("1"), 1)
        self.assertEqual(utils.validate_season("10"), 10)
    
    def test_validate_season_invalid(self):
        """Test invalid season validation."""
        with self.assertRaises(ValueError):
            utils.validate_season("0")  # Too low
        with self.assertRaises(ValueError):
            utils.validate_season("100")  # Too high
        with self.assertRaises(ValueError):
            utils.validate_season("abc")  # Not a number
    
    def test_validate_episode_valid(self):
        """Test valid episode validation."""
        self.assertEqual(utils.validate_episode("1"), 1)
        self.assertEqual(utils.validate_episode("500"), 500)
    
    def test_validate_episode_invalid(self):
        """Test invalid episode validation."""
        with self.assertRaises(ValueError):
            utils.validate_episode("0")  # Too low
        with self.assertRaises(ValueError):
            utils.validate_episode("2000")  # Too high
        with self.assertRaises(ValueError):
            utils.validate_episode("abc")  # Not a number
    
    def test_validate_limit_valid(self):
        """Test valid limit validation."""
        self.assertEqual(utils.validate_limit("10"), 10)
        self.assertEqual(utils.validate_limit("50"), 50)
    
    def test_validate_limit_invalid(self):
        """Test invalid limit validation."""
        with self.assertRaises(ValueError):
            utils.validate_limit("0")  # Too low
        with self.assertRaises(ValueError):
            utils.validate_limit("200")  # Too high
        with self.assertRaises(ValueError):
            utils.validate_limit("abc")  # Not a number


class TestFlattenResults(unittest.TestCase):
    """Test result flattening function."""
    
    def test_flatten_valid_results(self):
        """Test flattening valid results."""
        mock_subs = Mock()
        mock_subs.ZIPlinks = [
            ("Subtitle 1", "http://example.com/sub1.zip"),
            ("Subtitle 2", "http://example.com/sub2.zip")
        ]
        
        result = utils.flatten_results(mock_subs, "english")
        
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["name"], "Subtitle 1")
        self.assertEqual(result[0]["link"], "http://example.com/sub1.zip")
        self.assertEqual(result[0]["language"], "english")
    
    def test_flatten_no_ziplinks(self):
        """Test flattening when no ZIPlinks attribute."""
        mock_subs = Mock()
        del mock_subs.ZIPlinks
        
        result = utils.flatten_results(mock_subs, "english")
        
        self.assertEqual(result, [])
    
    def test_flatten_empty_results(self):
        """Test flattening empty results."""
        mock_subs = Mock()
        mock_subs.ZIPlinks = []
        
        result = utils.flatten_results(mock_subs, "english")
        
        self.assertEqual(result, [])


if __name__ == '__main__':
    unittest.main()
