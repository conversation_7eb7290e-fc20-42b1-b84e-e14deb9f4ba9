#!/usr/bin/env python3
"""Debug script to test the exact search scenario."""

import sys
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_exact_scenario():
    """Test the exact scenario from the GUI."""
    try:
        from subtitle_sources import MultiSourceSearcher
        
        print("Testing Exact GUI Scenario")
        print("=" * 40)
        
        # Create searcher (same as GUI)
        searcher = MultiSourceSearcher()
        print(f"Sources loaded: {[s.name for s in searcher.sources]}")
        
        # Test parameters from your screenshot
        query = "11.22.63"
        content_type = "episode"
        languages = "english"
        year = 2016
        season = 1
        episode = 1
        
        print(f"\nOriginal query: '{query}'")
        
        # Test original query (what manual database expects)
        print(f"\n1. Testing original query: '{query}'")
        results1 = searcher.search_combined(
            query=query,
            year=year,
            language="english",
            limit=50
        )
        print(f"   Results: {len(results1)}")
        for i, r in enumerate(results1[:3], 1):
            print(f"   {i}. [{r.get('source', 'Unknown')}] {r.get('name', 'Unknown')}")
        
        # Test episode-specific query (what GUI was doing)
        episode_query = f"{query} Season {season} Episode {episode}"
        print(f"\n2. Testing episode query: '{episode_query}'")
        results2 = searcher.search_combined(
            query=episode_query,
            year=year,
            language="english",
            limit=50
        )
        print(f"   Results: {len(results2)}")
        for i, r in enumerate(results2[:3], 1):
            print(f"   {i}. [{r.get('source', 'Unknown')}] {r.get('name', 'Unknown')}")
        
        # Test what the fixed GUI should do
        print(f"\n3. Testing fixed logic (try original first, then episode):")
        final_results = []
        
        # Try original first
        original_results = searcher.search_combined(
            query=query,
            year=year,
            language="english",
            limit=50
        )
        final_results.extend(original_results)
        
        # If no results, try episode-specific
        if not final_results:
            episode_results = searcher.search_combined(
                query=episode_query,
                year=year,
                language="english",
                limit=50
            )
            final_results.extend(episode_results)
        
        print(f"   Final results: {len(final_results)}")
        for i, r in enumerate(final_results[:5], 1):
            print(f"   {i}. [{r.get('source', 'Unknown')}] {r.get('name', 'Unknown')}")
        
        return len(final_results) > 0
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_source_directly():
    """Test the manual source directly."""
    try:
        from manual_sources import ManualSubtitleSource
        
        print("\nTesting Manual Source Directly")
        print("=" * 40)
        
        source = ManualSubtitleSource()
        
        test_queries = [
            "11.22.63",
            "11.22.63 Season 1 Episode 1",
            "jfk",
            "JFK"
        ]
        
        for query in test_queries:
            print(f"\nQuery: '{query}'")
            results = source.search(query, year=2016, language="english")
            print(f"Results: {len(results)}")
            for i, r in enumerate(results[:2], 1):
                print(f"  {i}. {r.get('name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"Error testing manual source: {e}")
        return False

if __name__ == "__main__":
    print("Debug Search Test")
    print("=" * 50)
    
    success1 = test_manual_source_directly()
    success2 = test_exact_scenario()
    
    if success1 and success2:
        print("\n✅ All tests passed!")
        print("\nThe manual source should work in the GUI now.")
        print("Try searching for '11.22.63' again.")
    else:
        print("\n❌ Some tests failed!")
        print("Check the error messages above.")
