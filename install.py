#!/usr/bin/env python3
"""Installation script for Subtitle Finder application."""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 6):
        print("Error: Python 3.6 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    return True

def install_dependencies():
    """Install required dependencies."""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("Error: requirements.txt not found.")
        return False
    
    print("Installing dependencies...")
    try:
        # Install main dependencies only (skip dev dependencies)
        main_deps = [
            "requests>=2.23.0,<3.0.0",
            "subsceneAPI==0.2"
        ]
        
        for dep in main_deps:
            print(f"Installing {dep}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"Error installing {dep}:")
                print(result.stderr)
                return False
        
        print("Dependencies installed successfully!")
        return True
        
    except Exception as e:
        print(f"Error installing dependencies: {e}")
        return False

def test_installation():
    """Test if the installation was successful."""
    print("Testing installation...")
    try:
        # Test imports
        import requests
        import subsceneAPI
        print("✓ All dependencies imported successfully")
        
        # Test basic functionality
        from utils import sanitize_filename, validate_url
        from downloader import fetch_and_save
        print("✓ All modules imported successfully")
        
        # Test basic functions
        test_filename = sanitize_filename("test<>file.srt")
        if test_filename == "test__file.srt":
            print("✓ Utility functions working correctly")
        else:
            print("✗ Utility function test failed")
            return False
        
        test_url = validate_url("https://example.com/test.srt")
        if test_url:
            print("✓ URL validation working correctly")
        else:
            print("✗ URL validation test failed")
            return False
        
        print("Installation test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Test error: {e}")
        return False

def create_desktop_shortcut():
    """Create a desktop shortcut (Windows only)."""
    if sys.platform != "win32":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Subtitle Finder.lnk")
        target = os.path.join(os.getcwd(), "subtitle_finder.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✓ Desktop shortcut created")
        
    except ImportError:
        print("Note: Install pywin32 to create desktop shortcuts")
    except Exception as e:
        print(f"Note: Could not create desktop shortcut: {e}")

def main():
    """Main installation function."""
    print("Subtitle Finder Installation Script")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    print(f"✓ Python {sys.version.split()[0]} detected")
    
    # Install dependencies
    if not install_dependencies():
        print("\nInstallation failed!")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("\nInstallation test failed!")
        sys.exit(1)
    
    # Create desktop shortcut (Windows only)
    create_desktop_shortcut()
    
    print("\n" + "=" * 40)
    print("Installation completed successfully!")
    print("\nTo run the application:")
    print("  python subtitle_finder.py")
    print("\nTo run tests:")
    print("  python run_tests.py")
    print("\nFor more information, see README.md")

if __name__ == "__main__":
    main()
