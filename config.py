"""Configuration settings for Subtitle Finder application."""

# Download limits
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
MAX_DOWNLOAD_TIMEOUT = 30  # seconds
CHUNK_SIZE = 8192  # bytes

# Search limits
MAX_QUERY_LENGTH = 200
MAX_SEARCH_LIMIT = 100
MIN_SEARCH_LIMIT = 1

# Year validation
MIN_YEAR = 1900
MAX_YEAR = 2030

# Season/Episode limits
MAX_SEASON = 50
MAX_EPISODE = 1000

# GUI settings
DEFAULT_WINDOW_SIZE = "800x600"
DEFAULT_OUTPUT_DIR = "./subtitles"
DEFAULT_LANGUAGE = "english"
DEFAULT_SEARCH_LIMIT = 50

# Supported file extensions
SUBTITLE_EXTENSIONS = (".srt", ".sub", ".vtt")
ARCHIVE_EXTENSIONS = (".zip",)

# Rate limiting
DOWNLOAD_DELAY = 1.0  # seconds between downloads

# Logging
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'
