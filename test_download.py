#!/usr/bin/env python3
"""Test the download functionality with data URLs."""

import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_data_url_download():
    """Test downloading from data URL."""
    try:
        from downloader import fetch_and_save
        
        # Test data URL (base64 encoded SRT content)
        data_url = "data:text/plain;base64,MSAwMDowMDowMCwwMDAgLS0+IDAwOjAwOjAzLDAwMAoKMQpXZWxjb21lIHRvIDExLjIyLjYzIEVwaXNvZGUgMSEKCjIKMDA6MDA6MDUsNTAwIC0tPiAwMDowMDowOCw1MDAKVGhpcyBpcyBhIHNhbXBsZSBzdWJ0aXRsZSBmb3IgZGVtb25zdHJhdGlvbi4KCjMKMDA6MDA6MTAsNTAwIC0tPiAwMDowMDoxMyw1MDAKUmVwbGFjZSB3aXRoIHJlYWwgc3VidGl0bGUgY29udGVudC4="
        
        output_dir = Path("./test_output")
        output_dir.mkdir(exist_ok=True)
        
        print("Testing data URL download...")
        result = fetch_and_save(data_url, output_dir, "11.22.63.S01E01.srt")
        
        print(f"✅ Success! File saved to: {result}")
        print(f"File exists: {result.exists()}")
        print(f"File size: {result.stat().st_size} bytes")
        
        # Read and display content
        content = result.read_text(encoding='utf-8')
        print(f"\nSubtitle content:")
        print("-" * 40)
        print(content)
        print("-" * 40)
        
        # Clean up
        result.unlink()
        output_dir.rmdir()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_source_integration():
    """Test the full manual source integration."""
    try:
        from subtitle_sources import MultiSourceSearcher
        from downloader import fetch_and_save
        
        print("\nTesting full integration...")
        
        # Search for subtitles
        searcher = MultiSourceSearcher()
        results = searcher.search_combined("11.22.63", year=2016, language="english", limit=1)
        
        if not results:
            print("❌ No results found")
            return False
        
        print(f"✅ Found {len(results)} results")
        
        # Try to download the first result
        first_result = results[0]
        print(f"Downloading: {first_result['name']}")
        print(f"From source: {first_result['source']}")
        print(f"URL: {first_result['link'][:50]}...")
        
        output_dir = Path("./test_output")
        output_dir.mkdir(exist_ok=True)
        
        downloaded_file = fetch_and_save(
            first_result['link'], 
            output_dir, 
            first_result['name'] + ".srt"
        )
        
        print(f"✅ Downloaded successfully to: {downloaded_file}")
        print(f"File size: {downloaded_file.stat().st_size} bytes")
        
        # Show first few lines
        content = downloaded_file.read_text(encoding='utf-8')
        lines = content.split('\n')[:6]
        print(f"\nFirst few lines:")
        for line in lines:
            print(f"  {line}")
        
        # Clean up
        downloaded_file.unlink()
        output_dir.rmdir()
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing Download Functionality")
    print("=" * 50)
    
    success1 = test_data_url_download()
    success2 = test_manual_source_integration()
    
    if success1 and success2:
        print("\n🎉 All tests passed!")
        print("The download functionality should now work in the GUI.")
    else:
        print("\n❌ Some tests failed!")
        print("Check the error messages above.")
